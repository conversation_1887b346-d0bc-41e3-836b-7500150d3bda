# 会员管理系统设计方案

## 1. 系统概述

本系统是一个完整的会员管理系统，主要用于管理会员信息、套餐类型、消费记录以及营业数据统计。系统采用现代化的Web技术栈，提供直观的用户界面和强大的数据分析功能。

## 2. 功能模块设计

### 2.1 套餐类型管理
- **套餐创建与编辑**
  - 套餐名称、描述
  - 套餐价格（原价、会员价、非会员价）
  - 套餐有效期（天数）
  - 套餐状态（启用/禁用）
  - 套餐分类（如：健身、美容、娱乐等）

- **套餐列表管理**
  - 套餐搜索与筛选
  - 套餐排序（按价格、创建时间等）
  - 批量操作（启用/禁用、删除）

### 2.2 会员管理
- **会员信息管理**
  - 基本信息：姓名、手机号、邮箱、生日
  - 会员等级：普通会员、VIP会员、钻石会员等
  - 会员状态：正常、冻结、过期
  - 注册时间、最后消费时间
  - 会员余额、积分

- **会员操作功能**
  - 会员注册与认证
  - 会员信息修改
  - 会员等级升级/降级
  - 会员状态管理
  - 会员搜索与筛选

### 2.3 消费套餐管理
- **消费记录管理**
  - 消费时间、消费金额
  - 消费套餐信息
  - 支付方式（现金、刷卡、移动支付等）
  - 消费状态（已完成、已退款、进行中）
  - 服务人员信息

- **会员消费功能**
  - 会员价格优惠
  - 积分累积与使用
  - 消费历史查询
  - 套餐使用记录

- **非会员消费功能**
  - 临时客户信息登记
  - 原价消费
  - 消费凭证生成

### 2.4 图表统计功能
- **营业额统计**
  - 按天统计：每日营业额趋势
  - 按月统计：月度营业额对比
  - 按年统计：年度营业额分析
  - 自定义时间段统计

- **数据分析维度**
  - 会员vs非会员消费占比
  - 套餐类型销售排行
  - 支付方式分布
  - 客户来源分析
  - 复购率统计

## 3. 技术架构设计

### 3.1 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI组件库**: 基于提供的设计系统（使用oklch颜色系统）
- **状态管理**: Zustand 或 Redux Toolkit
- **路由**: React Router v6
- **图表库**: Recharts 或 Chart.js
- **表单处理**: React Hook Form + Zod
- **HTTP客户端**: Axios

### 3.2 后端技术栈
- **数据库**: Supabase (PostgreSQL)
- **认证**: Supabase Auth
- **API**: Supabase REST API + RLS (Row Level Security)
- **实时功能**: Supabase Realtime

### 3.3 数据库设计

#### 用户表 (users)
```sql
- id (uuid, primary key)
- email (text, unique)
- phone (text, unique)
- name (text)
- birthday (date)
- member_level (enum: normal, vip, diamond)
- member_status (enum: active, frozen, expired)
- balance (decimal)
- points (integer)
- created_at (timestamp)
- updated_at (timestamp)
```

#### 套餐类型表 (package_types)
```sql
- id (uuid, primary key)
- name (text)
- description (text)
- category (text)
- original_price (decimal)
- member_price (decimal)
- non_member_price (decimal)
- validity_days (integer)
- status (enum: active, inactive)
- created_at (timestamp)
- updated_at (timestamp)
```

#### 消费记录表 (consumption_records)
```sql
- id (uuid, primary key)
- user_id (uuid, foreign key, nullable)
- package_type_id (uuid, foreign key)
- customer_name (text) -- 非会员客户姓名
- customer_phone (text) -- 非会员客户电话
- amount (decimal)
- payment_method (enum: cash, card, mobile)
- consumption_status (enum: completed, refunded, in_progress)
- staff_id (uuid, foreign key)
- consumed_at (timestamp)
- created_at (timestamp)
```

#### 员工表 (staff)
```sql
- id (uuid, primary key)
- name (text)
- role (enum: admin, staff)
- email (text, unique)
- created_at (timestamp)
```

## 4. 页面结构设计

### 4.1 主要页面
1. **仪表板 (Dashboard)**
   - 今日营业额概览
   - 会员统计
   - 热门套餐
   - 最近消费记录

2. **套餐管理 (Package Management)**
   - 套餐列表页
   - 套餐创建/编辑页
   - 套餐详情页

3. **会员管理 (Member Management)**
   - 会员列表页
   - 会员详情页
   - 会员注册页
   - 会员消费历史

4. **消费管理 (Consumption Management)**
   - 消费记录列表
   - 新增消费记录
   - 消费详情页

5. **统计报表 (Analytics)**
   - 营业额统计图表
   - 会员分析报告
   - 套餐销售分析

### 4.2 导航结构
```
├── 仪表板
├── 套餐管理
│   ├── 套餐列表
│   └── 新增套餐
├── 会员管理
│   ├── 会员列表
│   ├── 新增会员
│   └── 会员等级管理
├── 消费管理
│   ├── 消费记录
│   └── 新增消费
└── 统计报表
    ├── 营业额统计
    ├── 会员分析
    └── 套餐分析
```

## 5. 用户界面设计

### 5.1 设计原则
- 遵循提供的设计系统颜色规范
- 响应式设计，支持桌面和移动端
- 简洁直观的操作流程
- 数据可视化友好

### 5.2 主要组件
- 侧边栏导航
- 数据表格（支持排序、筛选、分页）
- 表单组件（输入框、选择器、日期选择器）
- 图表组件（柱状图、折线图、饼图）
- 模态框和抽屉组件
- 通知和提示组件

## 6. 开发计划

### 阶段一：基础架构搭建（1-2周）
- 项目初始化和环境配置
- 数据库设计和创建
- 基础组件库开发
- 路由和布局搭建

### 阶段二：核心功能开发（3-4周）
- 套餐管理功能
- 会员管理功能
- 消费记录功能
- 基础统计功能

### 阶段三：高级功能和优化（2-3周）
- 高级统计图表
- 数据导出功能
- 性能优化
- 测试和bug修复

### 阶段四：部署和上线（1周）
- 生产环境部署
- 用户培训
- 系统维护文档

## 7. 安全考虑

- 用户认证和授权
- 数据加密传输
- 敏感信息脱敏
- 操作日志记录
- 数据备份策略

## 8. 扩展性考虑

- 多门店支持
- 移动端APP
- 第三方支付集成
- 会员卡系统集成
- 营销活动管理

---

*本文档将随着项目进展持续更新和完善*