<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员管理系统 - 经典蓝色主题</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="member_management_blue_theme.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&family=Source+Serif+4:wght@400;600&display=swap" rel="stylesheet">
    
    <style>
        /* 动画样式 */
        .fade-in {
            animation: fadeIn 0.4s ease-out;
        }
        
        .slide-in-left {
            animation: slideInLeft 0.3s ease-out;
        }
        
        .bounce-in {
            animation: bounceIn 0.6s ease-out;
        }
        
        .hover-lift {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: var(--primary-foreground);
            transition: all 0.2s ease;
            border-radius: var(--radius);
        }
        
        .btn-primary:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-md);
        }
        
        .btn-primary:active {
            transform: scale(0.98);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        @keyframes bounceIn {
            0% { opacity: 0; transform: scale(0.95); }
            50% { transform: scale(1.02); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        .sidebar-active {
            background-color: var(--sidebar-accent);
            color: var(--sidebar-accent-foreground);
            border-right: 3px solid var(--primary);
        }
        
        .stat-card {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
        }
        
        .data-table {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }
        
        .table-row:hover {
            background-color: var(--muted);
        }
        
        .search-input {
            background: var(--background);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 0.5rem 1rem;
            transition: all 0.15s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--ring);
            box-shadow: 0 0 0 2px var(--ring);
            transform: scale(1.02);
        }
        
        /* 蓝色主题特色样式 */
        .blue-gradient {
            background: linear-gradient(135deg, var(--primary), var(--chart-2));
        }
        
        .tech-icon {
            color: var(--primary);
        }
        
        .professional-badge {
            background-color: var(--primary);
            color: var(--primary-foreground);
        }
    </style>
</head>
<body class="bg-background text-foreground font-sans">
    <!-- 主容器 -->
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-sidebar border-r border-sidebar-border flex flex-col">
            <!-- Logo区域 -->
            <div class="p-6 border-b border-sidebar-border">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 blue-gradient rounded-lg flex items-center justify-center">
                        <i data-lucide="building-2" class="w-5 h-5 text-white"></i>
                    </div>
                    <h1 class="text-xl font-semibold text-sidebar-foreground">企业会员系统</h1>
                </div>
            </div>
            
            <!-- 导航菜单 -->
            <nav class="flex-1 p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="#dashboard" class="sidebar-item sidebar-active flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200">
                            <i data-lucide="layout-dashboard" class="w-5 h-5"></i>
                            <span>仪表板</span>
                        </a>
                    </li>
                    <li>
                        <a href="#packages" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
                            <i data-lucide="package" class="w-5 h-5"></i>
                            <span>套餐管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="#members" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
                            <i data-lucide="users" class="w-5 h-5"></i>
                            <span>会员管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="#consumption" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
                            <i data-lucide="credit-card" class="w-5 h-5"></i>
                            <span>消费管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="#analytics" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
                            <i data-lucide="bar-chart-3" class="w-5 h-5"></i>
                            <span>统计报表</span>
                        </a>
                    </li>
                    <li>
                        <a href="#settings" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
                            <i data-lucide="settings" class="w-5 h-5"></i>
                            <span>系统设置</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <!-- 用户信息 -->
            <div class="p-4 border-t border-sidebar-border">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 blue-gradient rounded-full flex items-center justify-center">
                        <i data-lucide="user" class="w-4 h-4 text-white"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-sidebar-foreground">系统管理员</p>
                        <p class="text-xs text-muted-foreground"><EMAIL></p>
                    </div>
                    <button class="p-1 hover:bg-sidebar-accent rounded">
                        <i data-lucide="log-out" class="w-4 h-4 text-muted-foreground"></i>
                    </button>
                </div>
            </div>
        </aside>
        
        <!-- 主内容区域 -->
        <main class="flex-1 flex flex-col overflow-hidden">
            <!-- 顶部导航栏 -->
            <header class="bg-card border-b border-border px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <nav class="text-sm text-muted-foreground">
                            <span>首页</span>
                            <span class="mx-2">/</span>
                            <span class="text-foreground">仪表板</span>
                        </nav>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="p-2 hover:bg-muted rounded-lg transition-colors">
                            <i data-lucide="bell" class="w-5 h-5"></i>
                        </button>
                        <button class="p-2 hover:bg-muted rounded-lg transition-colors">
                            <i data-lucide="moon" class="w-5 h-5"></i>
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 页面内容 -->
            <div class="flex-1 overflow-auto p-6">
                <!-- 仪表板页面 -->
                <div id="dashboard-content" class="space-y-6">
                    <!-- 页面标题 -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-2xl font-semibold text-foreground">企业仪表板</h2>
                            <p class="text-muted-foreground">专业的会员管理解决方案</p>
                        </div>
                        <div class="flex items-center space-x-3">
                            <select class="search-input">
                                <option>今天</option>
                                <option>本周</option>
                                <option>本月</option>
                                <option>本年</option>
                            </select>
                            <button class="btn-primary px-4 py-2 rounded-lg flex items-center space-x-2">
                                <i data-lucide="download" class="w-4 h-4"></i>
                                <span>导出报表</span>
                            </button>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="stat-card hover-lift fade-in">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-muted-foreground">今日营收</p>
                                    <p class="text-2xl font-bold text-foreground">¥12,888</p>
                                    <p class="text-xs text-blue-600">+18.5% 较昨日</p>
                                </div>
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="trending-up" class="w-6 h-6 text-blue-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card hover-lift fade-in" style="animation-delay: 0.1s">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-muted-foreground">本月营收</p>
                                    <p class="text-2xl font-bold text-foreground">¥128,888</p>
                                    <p class="text-xs text-blue-600">+12.2% 较上月</p>
                                </div>
                                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="dollar-sign" class="w-6 h-6 text-indigo-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card hover-lift fade-in" style="animation-delay: 0.2s">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-muted-foreground">企业会员</p>
                                    <p class="text-2xl font-bold text-foreground">2,468</p>
                                    <p class="text-xs text-blue-600">+25 新增</p>
                                </div>
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="building" class="w-6 h-6 text-purple-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card hover-lift fade-in" style="animation-delay: 0.3s">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-muted-foreground">服务套餐</p>
                                    <p class="text-2xl font-bold text-foreground">68</p>
                                    <p class="text-xs text-muted-foreground">8 个分类</p>
                                </div>
                                <div class="w-12 h-12 bg-cyan-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="briefcase" class="w-6 h-6 text-cyan-600"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表区域 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 营收趋势图 -->
                        <div class="stat-card">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">营收趋势分析</h3>
                                <select class="search-input text-sm">
                                    <option>最近7天</option>
                                    <option>最近30天</option>
                                    <option>最近90天</option>
                                </select>
                            </div>
                            <div class="h-64">
                                <canvas id="revenueChart"></canvas>
                            </div>
                        </div>

                        <!-- 企业套餐排行 -->
                        <div class="stat-card">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">热门企业套餐</h3>
                                <button class="text-sm text-primary hover:underline">查看全部</button>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-muted rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                                            <span class="text-sm font-bold text-primary-foreground">1</span>
                                        </div>
                                        <div>
                                            <p class="font-medium">企业培训套餐</p>
                                            <p class="text-sm text-muted-foreground">销量: 286</p>
                                        </div>
                                    </div>
                                    <span class="text-lg font-bold text-blue-600">¥2,999</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-muted rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center">
                                            <span class="text-sm font-bold text-white">2</span>
                                        </div>
                                        <div>
                                            <p class="font-medium">团队建设套餐</p>
                                            <p class="text-sm text-muted-foreground">销量: 198</p>
                                        </div>
                                    </div>
                                    <span class="text-lg font-bold text-blue-600">¥1,599</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-muted rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                                            <span class="text-sm font-bold text-white">3</span>
                                        </div>
                                        <div>
                                            <p class="font-medium">咨询服务套餐</p>
                                            <p class="text-sm text-muted-foreground">销量: 156</p>
                                        </div>
                                    </div>
                                    <span class="text-lg font-bold text-blue-600">¥899</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();

        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = ['dashboard-content'];
            pages.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.classList.add('hidden');
                }
            });

            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.remove('hidden');
                targetPage.classList.add('fade-in');
            }

            // 更新侧边栏激活状态
            document.querySelectorAll('.sidebar-item').forEach(item => {
                item.classList.remove('sidebar-active');
            });

            // 激活当前页面的侧边栏项
            const activeItem = document.querySelector(`[href="#${pageId.replace('-content', '')}"]`);
            if (activeItem) {
                activeItem.classList.add('sidebar-active');
            }
        }

        // 侧边栏导航事件
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const href = item.getAttribute('href');
                const pageId = href.substring(1) + '-content';
                showPage(pageId);
            });
        });

        // 初始化营收趋势图表
        function initRevenueChart() {
            const ctx = document.getElementById('revenueChart');
            if (!ctx) return;

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    datasets: [{
                        label: '营收',
                        data: [12500, 15200, 13800, 16500, 18200, 21800, 19800],
                        borderColor: 'oklch(0.6231 0.1880 259.8145)',
                        backgroundColor: 'oklch(0.6231 0.1880 259.8145 / 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    elements: {
                        point: {
                            radius: 4,
                            hoverRadius: 6
                        }
                    }
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initRevenueChart();

            // 添加页面加载动画
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.3s ease';
                document.body.style.opacity = '1';
            }, 100);
        });

        // 暗色模式切换
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
            localStorage.setItem('darkMode', document.documentElement.classList.contains('dark'));
        }

        // 加载保存的主题设置
        if (localStorage.getItem('darkMode') === 'true') {
            document.documentElement.classList.add('dark');
        }

        // 绑定暗色模式切换按钮
        document.querySelector('[data-lucide="moon"]').parentElement.addEventListener('click', toggleDarkMode);
    </script>
</body>
</html>
