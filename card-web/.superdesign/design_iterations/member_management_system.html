<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="member_management_theme.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&family=Source+Serif+4:wght@400;600&display=swap" rel="stylesheet">
    
    <style>
        /* 动画样式 */
        .fade-in {
            animation: fadeIn 0.4s ease-out;
        }
        
        .slide-in-left {
            animation: slideInLeft 0.3s ease-out;
        }
        
        .bounce-in {
            animation: bounceIn 0.6s ease-out;
        }
        
        .hover-lift {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: var(--primary-foreground);
            transition: all 0.2s ease;
        }
        
        .btn-primary:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-md);
        }
        
        .btn-primary:active {
            transform: scale(0.98);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        @keyframes bounceIn {
            0% { opacity: 0; transform: scale(0.95); }
            50% { transform: scale(1.02); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        .sidebar-active {
            background-color: var(--sidebar-accent);
            color: var(--sidebar-accent-foreground);
            border-right: 3px solid var(--primary);
        }
        
        .stat-card {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
        }
        
        .data-table {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }
        
        .table-row:hover {
            background-color: var(--muted);
        }
        
        .search-input {
            background: var(--background);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 0.5rem 1rem;
            transition: all 0.15s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--ring);
            box-shadow: 0 0 0 2px var(--ring);
            transform: scale(1.02);
        }
    </style>
</head>
<body class="bg-background text-foreground font-sans">
    <!-- 主容器 -->
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-sidebar border-r border-sidebar-border flex flex-col">
            <!-- Logo区域 -->
            <div class="p-6 border-b border-sidebar-border">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                        <i data-lucide="users" class="w-5 h-5 text-primary-foreground"></i>
                    </div>
                    <h1 class="text-xl font-semibold text-sidebar-foreground">会员管理系统</h1>
                </div>
            </div>
            
            <!-- 导航菜单 -->
            <nav class="flex-1 p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="#dashboard" class="sidebar-item sidebar-active flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200">
                            <i data-lucide="layout-dashboard" class="w-5 h-5"></i>
                            <span>仪表板</span>
                        </a>
                    </li>
                    <li>
                        <a href="#packages" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
                            <i data-lucide="package" class="w-5 h-5"></i>
                            <span>套餐管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="#members" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
                            <i data-lucide="users" class="w-5 h-5"></i>
                            <span>会员管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="#consumption" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
                            <i data-lucide="credit-card" class="w-5 h-5"></i>
                            <span>消费管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="#analytics" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
                            <i data-lucide="bar-chart-3" class="w-5 h-5"></i>
                            <span>统计报表</span>
                        </a>
                    </li>
                    <li>
                        <a href="#settings" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
                            <i data-lucide="settings" class="w-5 h-5"></i>
                            <span>系统设置</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <!-- 用户信息 -->
            <div class="p-4 border-t border-sidebar-border">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                        <i data-lucide="user" class="w-4 h-4 text-primary-foreground"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-sidebar-foreground">管理员</p>
                        <p class="text-xs text-muted-foreground"><EMAIL></p>
                    </div>
                    <button class="p-1 hover:bg-sidebar-accent rounded">
                        <i data-lucide="log-out" class="w-4 h-4 text-muted-foreground"></i>
                    </button>
                </div>
            </div>
        </aside>
        
        <!-- 主内容区域 -->
        <main class="flex-1 flex flex-col overflow-hidden">
            <!-- 顶部导航栏 -->
            <header class="bg-card border-b border-border px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <nav class="text-sm text-muted-foreground">
                            <span>首页</span>
                            <span class="mx-2">/</span>
                            <span class="text-foreground">仪表板</span>
                        </nav>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="p-2 hover:bg-muted rounded-lg transition-colors">
                            <i data-lucide="bell" class="w-5 h-5"></i>
                        </button>
                        <button class="p-2 hover:bg-muted rounded-lg transition-colors">
                            <i data-lucide="moon" class="w-5 h-5"></i>
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 页面内容 -->
            <div class="flex-1 overflow-auto p-6">
                <!-- 仪表板页面 -->
                <div id="dashboard-content" class="space-y-6">
                    <!-- 页面标题 -->
                    <div class="flex items-center justify-between">
                        <h2 class="text-2xl font-semibold text-foreground">仪表板</h2>
                        <div class="flex items-center space-x-3">
                            <select class="search-input">
                                <option>今天</option>
                                <option>本周</option>
                                <option>本月</option>
                                <option>本年</option>
                            </select>
                            <button class="btn-primary px-4 py-2 rounded-lg flex items-center space-x-2">
                                <i data-lucide="download" class="w-4 h-4"></i>
                                <span>导出报表</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 统计卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="stat-card hover-lift fade-in">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-muted-foreground">今日营收</p>
                                    <p class="text-2xl font-bold text-foreground">¥8,888</p>
                                    <p class="text-xs text-green-600">+12.5% 较昨日</p>
                                </div>
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="trending-up" class="w-6 h-6 text-green-600"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card hover-lift fade-in" style="animation-delay: 0.1s">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-muted-foreground">本月营收</p>
                                    <p class="text-2xl font-bold text-foreground">¥88,888</p>
                                    <p class="text-xs text-green-600">+8.2% 较上月</p>
                                </div>
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="dollar-sign" class="w-6 h-6 text-blue-600"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card hover-lift fade-in" style="animation-delay: 0.2s">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-muted-foreground">会员总数</p>
                                    <p class="text-2xl font-bold text-foreground">1,234</p>
                                    <p class="text-xs text-green-600">+15 新增</p>
                                </div>
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="users" class="w-6 h-6 text-purple-600"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card hover-lift fade-in" style="animation-delay: 0.3s">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-muted-foreground">套餐总数</p>
                                    <p class="text-2xl font-bold text-foreground">45</p>
                                    <p class="text-xs text-muted-foreground">5 个分类</p>
                                </div>
                                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="package" class="w-6 h-6 text-orange-600"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表区域 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 营收趋势图 -->
                        <div class="stat-card">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">营收趋势</h3>
                                <select class="search-input text-sm">
                                    <option>最近7天</option>
                                    <option>最近30天</option>
                                    <option>最近90天</option>
                                </select>
                            </div>
                            <div class="h-64">
                                <canvas id="revenueChart"></canvas>
                            </div>
                        </div>

                        <!-- 套餐销售排行 -->
                        <div class="stat-card">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">热门套餐</h3>
                                <button class="text-sm text-primary hover:underline">查看全部</button>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-muted rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                                            <span class="text-sm font-bold text-primary-foreground">1</span>
                                        </div>
                                        <div>
                                            <p class="font-medium">健身套餐A</p>
                                            <p class="text-sm text-muted-foreground">销量: 156</p>
                                        </div>
                                    </div>
                                    <span class="text-lg font-bold text-green-600">¥299</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-muted rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center">
                                            <span class="text-sm font-bold text-secondary-foreground">2</span>
                                        </div>
                                        <div>
                                            <p class="font-medium">美容套餐B</p>
                                            <p class="text-sm text-muted-foreground">销量: 128</p>
                                        </div>
                                    </div>
                                    <span class="text-lg font-bold text-green-600">¥599</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-muted rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                                            <span class="text-sm font-bold text-accent-foreground">3</span>
                                        </div>
                                        <div>
                                            <p class="font-medium">娱乐套餐C</p>
                                            <p class="text-sm text-muted-foreground">销量: 95</p>
                                        </div>
                                    </div>
                                    <span class="text-lg font-bold text-green-600">¥199</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近活动 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 最新会员 -->
                        <div class="stat-card">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">最新会员</h3>
                                <button class="text-sm text-primary hover:underline">查看全部</button>
                            </div>
                            <div class="space-y-3">
                                <div class="flex items-center space-x-3 p-3 hover:bg-muted rounded-lg transition-colors">
                                    <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                                        <span class="text-sm font-bold text-primary-foreground">张</span>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium">张三</p>
                                        <p class="text-sm text-muted-foreground">VIP会员 • 2小时前加入</p>
                                    </div>
                                    <span class="text-sm text-green-600">¥1,299</span>
                                </div>

                                <div class="flex items-center space-x-3 p-3 hover:bg-muted rounded-lg transition-colors">
                                    <div class="w-10 h-10 bg-secondary rounded-full flex items-center justify-center">
                                        <span class="text-sm font-bold text-secondary-foreground">李</span>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium">李四</p>
                                        <p class="text-sm text-muted-foreground">普通会员 • 5小时前加入</p>
                                    </div>
                                    <span class="text-sm text-green-600">¥599</span>
                                </div>

                                <div class="flex items-center space-x-3 p-3 hover:bg-muted rounded-lg transition-colors">
                                    <div class="w-10 h-10 bg-accent rounded-full flex items-center justify-center">
                                        <span class="text-sm font-bold text-accent-foreground">王</span>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium">王五</p>
                                        <p class="text-sm text-muted-foreground">钻石会员 • 1天前加入</p>
                                    </div>
                                    <span class="text-sm text-green-600">¥2,999</span>
                                </div>
                            </div>
                        </div>

                        <!-- 最近消费 -->
                        <div class="stat-card">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">最近消费</h3>
                                <button class="text-sm text-primary hover:underline">查看全部</button>
                            </div>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 hover:bg-muted rounded-lg transition-colors">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <div>
                                            <p class="font-medium">健身套餐A</p>
                                            <p class="text-sm text-muted-foreground">张三 • 30分钟前</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-bold">¥299</span>
                                </div>

                                <div class="flex items-center justify-between p-3 hover:bg-muted rounded-lg transition-colors">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                        <div>
                                            <p class="font-medium">美容套餐B</p>
                                            <p class="text-sm text-muted-foreground">李四 • 1小时前</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-bold">¥599</span>
                                </div>

                                <div class="flex items-center justify-between p-3 hover:bg-muted rounded-lg transition-colors">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                                        <div>
                                            <p class="font-medium">娱乐套餐C</p>
                                            <p class="text-sm text-muted-foreground">王五 • 2小时前</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-bold">¥199</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 套餐管理页面 (隐藏) -->
                <div id="packages-content" class="space-y-6 hidden">
                    <div class="flex items-center justify-between">
                        <h2 class="text-2xl font-semibold text-foreground">套餐管理</h2>
                        <button class="btn-primary px-4 py-2 rounded-lg flex items-center space-x-2">
                            <i data-lucide="plus" class="w-4 h-4"></i>
                            <span>新增套餐</span>
                        </button>
                    </div>

                    <!-- 搜索和筛选 -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="relative">
                                <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"></i>
                                <input type="text" placeholder="搜索套餐名称..." class="search-input pl-10 w-64">
                            </div>
                            <select class="search-input">
                                <option>全部分类</option>
                                <option>健身</option>
                                <option>美容</option>
                                <option>娱乐</option>
                            </select>
                            <select class="search-input">
                                <option>全部状态</option>
                                <option>启用</option>
                                <option>禁用</option>
                            </select>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="p-2 hover:bg-muted rounded-lg">
                                <i data-lucide="filter" class="w-4 h-4"></i>
                            </button>
                            <button class="p-2 hover:bg-muted rounded-lg">
                                <i data-lucide="download" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 套餐表格 -->
                    <div class="data-table">
                        <table class="w-full">
                            <thead class="bg-muted">
                                <tr>
                                    <th class="text-left p-4 font-medium">套餐名称</th>
                                    <th class="text-left p-4 font-medium">分类</th>
                                    <th class="text-left p-4 font-medium">原价</th>
                                    <th class="text-left p-4 font-medium">会员价</th>
                                    <th class="text-left p-4 font-medium">有效期</th>
                                    <th class="text-left p-4 font-medium">状态</th>
                                    <th class="text-left p-4 font-medium">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="table-row border-b border-border">
                                    <td class="p-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                                                <i data-lucide="dumbbell" class="w-5 h-5 text-primary-foreground"></i>
                                            </div>
                                            <div>
                                                <p class="font-medium">健身套餐A</p>
                                                <p class="text-sm text-muted-foreground">基础健身训练</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">健身</span>
                                    </td>
                                    <td class="p-4 font-medium">¥399</td>
                                    <td class="p-4 font-medium text-green-600">¥299</td>
                                    <td class="p-4">30天</td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">启用</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center space-x-2">
                                            <button class="p-1 hover:bg-muted rounded">
                                                <i data-lucide="edit" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded text-destructive">
                                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="table-row border-b border-border">
                                    <td class="p-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-secondary rounded-lg flex items-center justify-center">
                                                <i data-lucide="sparkles" class="w-5 h-5 text-secondary-foreground"></i>
                                            </div>
                                            <div>
                                                <p class="font-medium">美容套餐B</p>
                                                <p class="text-sm text-muted-foreground">专业美容护理</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-pink-100 text-pink-800 rounded-full text-xs">美容</span>
                                    </td>
                                    <td class="p-4 font-medium">¥799</td>
                                    <td class="p-4 font-medium text-green-600">¥599</td>
                                    <td class="p-4">60天</td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">启用</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center space-x-2">
                                            <button class="p-1 hover:bg-muted rounded">
                                                <i data-lucide="edit" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded text-destructive">
                                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="table-row border-b border-border">
                                    <td class="p-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-accent rounded-lg flex items-center justify-center">
                                                <i data-lucide="gamepad-2" class="w-5 h-5 text-accent-foreground"></i>
                                            </div>
                                            <div>
                                                <p class="font-medium">娱乐套餐C</p>
                                                <p class="text-sm text-muted-foreground">休闲娱乐体验</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">娱乐</span>
                                    </td>
                                    <td class="p-4 font-medium">¥299</td>
                                    <td class="p-4 font-medium text-green-600">¥199</td>
                                    <td class="p-4">15天</td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">禁用</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center space-x-2">
                                            <button class="p-1 hover:bg-muted rounded">
                                                <i data-lucide="edit" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded text-destructive">
                                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="flex items-center justify-between">
                        <p class="text-sm text-muted-foreground">显示 1-10 条，共 45 条记录</p>
                        <div class="flex items-center space-x-2">
                            <button class="px-3 py-1 border border-border rounded hover:bg-muted">上一页</button>
                            <button class="px-3 py-1 bg-primary text-primary-foreground rounded">1</button>
                            <button class="px-3 py-1 border border-border rounded hover:bg-muted">2</button>
                            <button class="px-3 py-1 border border-border rounded hover:bg-muted">3</button>
                            <button class="px-3 py-1 border border-border rounded hover:bg-muted">下一页</button>
                        </div>
                    </div>
                </div>

                <!-- 会员管理页面 (隐藏) -->
                <div id="members-content" class="space-y-6 hidden">
                    <div class="flex items-center justify-between">
                        <h2 class="text-2xl font-semibold text-foreground">会员管理</h2>
                        <button class="btn-primary px-4 py-2 rounded-lg flex items-center space-x-2">
                            <i data-lucide="user-plus" class="w-4 h-4"></i>
                            <span>新增会员</span>
                        </button>
                    </div>

                    <!-- 搜索和筛选 -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="relative">
                                <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"></i>
                                <input type="text" placeholder="搜索会员姓名或手机号..." class="search-input pl-10 w-64">
                            </div>
                            <select class="search-input">
                                <option>全部等级</option>
                                <option>普通会员</option>
                                <option>VIP会员</option>
                                <option>钻石会员</option>
                            </select>
                            <select class="search-input">
                                <option>全部状态</option>
                                <option>正常</option>
                                <option>冻结</option>
                                <option>过期</option>
                            </select>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="p-2 hover:bg-muted rounded-lg">
                                <i data-lucide="filter" class="w-4 h-4"></i>
                            </button>
                            <button class="p-2 hover:bg-muted rounded-lg">
                                <i data-lucide="download" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 会员表格 -->
                    <div class="data-table">
                        <table class="w-full">
                            <thead class="bg-muted">
                                <tr>
                                    <th class="text-left p-4 font-medium">会员信息</th>
                                    <th class="text-left p-4 font-medium">等级</th>
                                    <th class="text-left p-4 font-medium">余额</th>
                                    <th class="text-left p-4 font-medium">积分</th>
                                    <th class="text-left p-4 font-medium">注册时间</th>
                                    <th class="text-left p-4 font-medium">状态</th>
                                    <th class="text-left p-4 font-medium">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="table-row border-b border-border">
                                    <td class="p-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                                                <span class="text-sm font-bold text-primary-foreground">张</span>
                                            </div>
                                            <div>
                                                <p class="font-medium">张三</p>
                                                <p class="text-sm text-muted-foreground">138****8888</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">VIP会员</span>
                                    </td>
                                    <td class="p-4 font-medium">¥1,299</td>
                                    <td class="p-4 font-medium">2,580</td>
                                    <td class="p-4 text-sm">2024-01-15</td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">正常</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center space-x-2">
                                            <button class="p-1 hover:bg-muted rounded" title="查看详情">
                                                <i data-lucide="eye" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded" title="编辑">
                                                <i data-lucide="edit" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded" title="充值">
                                                <i data-lucide="credit-card" class="w-4 h-4"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="table-row border-b border-border">
                                    <td class="p-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-secondary rounded-full flex items-center justify-center">
                                                <span class="text-sm font-bold text-secondary-foreground">李</span>
                                            </div>
                                            <div>
                                                <p class="font-medium">李四</p>
                                                <p class="text-sm text-muted-foreground">139****9999</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">普通会员</span>
                                    </td>
                                    <td class="p-4 font-medium">¥599</td>
                                    <td class="p-4 font-medium">1,200</td>
                                    <td class="p-4 text-sm">2024-02-20</td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">正常</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center space-x-2">
                                            <button class="p-1 hover:bg-muted rounded" title="查看详情">
                                                <i data-lucide="eye" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded" title="编辑">
                                                <i data-lucide="edit" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded" title="充值">
                                                <i data-lucide="credit-card" class="w-4 h-4"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="table-row border-b border-border">
                                    <td class="p-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-accent rounded-full flex items-center justify-center">
                                                <span class="text-sm font-bold text-accent-foreground">王</span>
                                            </div>
                                            <div>
                                                <p class="font-medium">王五</p>
                                                <p class="text-sm text-muted-foreground">137****7777</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">钻石会员</span>
                                    </td>
                                    <td class="p-4 font-medium">¥2,999</td>
                                    <td class="p-4 font-medium">5,800</td>
                                    <td class="p-4 text-sm">2023-12-10</td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">正常</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center space-x-2">
                                            <button class="p-1 hover:bg-muted rounded" title="查看详情">
                                                <i data-lucide="eye" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded" title="编辑">
                                                <i data-lucide="edit" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded" title="充值">
                                                <i data-lucide="credit-card" class="w-4 h-4"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="flex items-center justify-between">
                        <p class="text-sm text-muted-foreground">显示 1-10 条，共 1,234 条记录</p>
                        <div class="flex items-center space-x-2">
                            <button class="px-3 py-1 border border-border rounded hover:bg-muted">上一页</button>
                            <button class="px-3 py-1 bg-primary text-primary-foreground rounded">1</button>
                            <button class="px-3 py-1 border border-border rounded hover:bg-muted">2</button>
                            <button class="px-3 py-1 border border-border rounded hover:bg-muted">3</button>
                            <button class="px-3 py-1 border border-border rounded hover:bg-muted">下一页</button>
                        </div>
                    </div>
                </div>

                <!-- 消费管理页面 (隐藏) -->
                <div id="consumption-content" class="space-y-6 hidden">
                    <div class="flex items-center justify-between">
                        <h2 class="text-2xl font-semibold text-foreground">消费管理</h2>
                        <button class="btn-primary px-4 py-2 rounded-lg flex items-center space-x-2">
                            <i data-lucide="plus" class="w-4 h-4"></i>
                            <span>新增消费</span>
                        </button>
                    </div>

                    <!-- 搜索和筛选 -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="relative">
                                <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"></i>
                                <input type="text" placeholder="搜索客户姓名或套餐..." class="search-input pl-10 w-64">
                            </div>
                            <select class="search-input">
                                <option>全部类型</option>
                                <option>会员消费</option>
                                <option>非会员消费</option>
                            </select>
                            <select class="search-input">
                                <option>全部状态</option>
                                <option>已完成</option>
                                <option>进行中</option>
                                <option>已退款</option>
                            </select>
                            <input type="date" class="search-input">
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="p-2 hover:bg-muted rounded-lg">
                                <i data-lucide="filter" class="w-4 h-4"></i>
                            </button>
                            <button class="p-2 hover:bg-muted rounded-lg">
                                <i data-lucide="download" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 消费记录表格 -->
                    <div class="data-table">
                        <table class="w-full">
                            <thead class="bg-muted">
                                <tr>
                                    <th class="text-left p-4 font-medium">客户信息</th>
                                    <th class="text-left p-4 font-medium">套餐</th>
                                    <th class="text-left p-4 font-medium">金额</th>
                                    <th class="text-left p-4 font-medium">支付方式</th>
                                    <th class="text-left p-4 font-medium">消费时间</th>
                                    <th class="text-left p-4 font-medium">状态</th>
                                    <th class="text-left p-4 font-medium">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="table-row border-b border-border">
                                    <td class="p-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                                                <span class="text-sm font-bold text-primary-foreground">张</span>
                                            </div>
                                            <div>
                                                <p class="font-medium">张三</p>
                                                <p class="text-sm text-muted-foreground">VIP会员</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <div>
                                            <p class="font-medium">健身套餐A</p>
                                            <p class="text-sm text-muted-foreground">基础健身训练</p>
                                        </div>
                                    </td>
                                    <td class="p-4 font-medium text-green-600">¥299</td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">移动支付</span>
                                    </td>
                                    <td class="p-4 text-sm">2024-03-15 14:30</td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">已完成</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center space-x-2">
                                            <button class="p-1 hover:bg-muted rounded" title="查看详情">
                                                <i data-lucide="eye" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded" title="打印小票">
                                                <i data-lucide="printer" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded text-destructive" title="退款">
                                                <i data-lucide="undo" class="w-4 h-4"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="table-row border-b border-border">
                                    <td class="p-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-gray-400 rounded-full flex items-center justify-center">
                                                <span class="text-sm font-bold text-white">临</span>
                                            </div>
                                            <div>
                                                <p class="font-medium">临时客户</p>
                                                <p class="text-sm text-muted-foreground">非会员</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <div>
                                            <p class="font-medium">美容套餐B</p>
                                            <p class="text-sm text-muted-foreground">专业美容护理</p>
                                        </div>
                                    </td>
                                    <td class="p-4 font-medium text-green-600">¥799</td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">现金</span>
                                    </td>
                                    <td class="p-4 text-sm">2024-03-15 16:45</td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">已完成</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center space-x-2">
                                            <button class="p-1 hover:bg-muted rounded" title="查看详情">
                                                <i data-lucide="eye" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded" title="打印小票">
                                                <i data-lucide="printer" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded text-destructive" title="退款">
                                                <i data-lucide="undo" class="w-4 h-4"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="table-row border-b border-border">
                                    <td class="p-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-secondary rounded-full flex items-center justify-center">
                                                <span class="text-sm font-bold text-secondary-foreground">李</span>
                                            </div>
                                            <div>
                                                <p class="font-medium">李四</p>
                                                <p class="text-sm text-muted-foreground">普通会员</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <div>
                                            <p class="font-medium">娱乐套餐C</p>
                                            <p class="text-sm text-muted-foreground">休闲娱乐体验</p>
                                        </div>
                                    </td>
                                    <td class="p-4 font-medium text-green-600">¥199</td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">刷卡</span>
                                    </td>
                                    <td class="p-4 text-sm">2024-03-14 20:15</td>
                                    <td class="p-4">
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">进行中</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center space-x-2">
                                            <button class="p-1 hover:bg-muted rounded" title="查看详情">
                                                <i data-lucide="eye" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded" title="打印小票">
                                                <i data-lucide="printer" class="w-4 h-4"></i>
                                            </button>
                                            <button class="p-1 hover:bg-muted rounded text-destructive" title="退款">
                                                <i data-lucide="undo" class="w-4 h-4"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="flex items-center justify-between">
                        <p class="text-sm text-muted-foreground">显示 1-10 条，共 2,856 条记录</p>
                        <div class="flex items-center space-x-2">
                            <button class="px-3 py-1 border border-border rounded hover:bg-muted">上一页</button>
                            <button class="px-3 py-1 bg-primary text-primary-foreground rounded">1</button>
                            <button class="px-3 py-1 border border-border rounded hover:bg-muted">2</button>
                            <button class="px-3 py-1 border border-border rounded hover:bg-muted">3</button>
                            <button class="px-3 py-1 border border-border rounded hover:bg-muted">下一页</button>
                        </div>
                    </div>
                </div>

                <!-- 统计报表页面 (隐藏) -->
                <div id="analytics-content" class="space-y-6 hidden">
                    <div class="flex items-center justify-between">
                        <h2 class="text-2xl font-semibold text-foreground">统计报表</h2>
                        <div class="flex items-center space-x-3">
                            <select class="search-input">
                                <option>最近7天</option>
                                <option>最近30天</option>
                                <option>最近90天</option>
                                <option>自定义时间</option>
                            </select>
                            <button class="btn-primary px-4 py-2 rounded-lg flex items-center space-x-2">
                                <i data-lucide="download" class="w-4 h-4"></i>
                                <span>导出报表</span>
                            </button>
                        </div>
                    </div>

                    <!-- 统计概览卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="stat-card hover-lift">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-muted-foreground">总营收</p>
                                    <p class="text-2xl font-bold text-foreground">¥888,888</p>
                                    <p class="text-xs text-green-600">+15.2% 较上期</p>
                                </div>
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="trending-up" class="w-6 h-6 text-green-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card hover-lift">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-muted-foreground">订单总数</p>
                                    <p class="text-2xl font-bold text-foreground">2,856</p>
                                    <p class="text-xs text-green-600">+8.5% 较上期</p>
                                </div>
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="shopping-cart" class="w-6 h-6 text-blue-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card hover-lift">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-muted-foreground">平均客单价</p>
                                    <p class="text-2xl font-bold text-foreground">¥311</p>
                                    <p class="text-xs text-green-600">+3.2% 较上期</p>
                                </div>
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="calculator" class="w-6 h-6 text-purple-600"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card hover-lift">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-muted-foreground">会员转化率</p>
                                    <p class="text-2xl font-bold text-foreground">68.5%</p>
                                    <p class="text-xs text-green-600">+2.1% 较上期</p>
                                </div>
                                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="percent" class="w-6 h-6 text-orange-600"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表区域 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 营收趋势图 -->
                        <div class="stat-card">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">营收趋势分析</h3>
                                <div class="flex items-center space-x-2">
                                    <button class="text-sm px-3 py-1 bg-primary text-primary-foreground rounded">日</button>
                                    <button class="text-sm px-3 py-1 hover:bg-muted rounded">周</button>
                                    <button class="text-sm px-3 py-1 hover:bg-muted rounded">月</button>
                                </div>
                            </div>
                            <div class="h-64">
                                <canvas id="analyticsRevenueChart"></canvas>
                            </div>
                        </div>

                        <!-- 会员vs非会员消费占比 -->
                        <div class="stat-card">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">消费类型分布</h3>
                                <button class="text-sm text-primary hover:underline">查看详情</button>
                            </div>
                            <div class="h-64">
                                <canvas id="membershipChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 详细统计表格 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 套餐销售排行 -->
                        <div class="stat-card">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">套餐销售排行</h3>
                                <button class="text-sm text-primary hover:underline">查看全部</button>
                            </div>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-muted rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                                            <span class="text-sm font-bold text-primary-foreground">1</span>
                                        </div>
                                        <div>
                                            <p class="font-medium">健身套餐A</p>
                                            <p class="text-sm text-muted-foreground">销量: 156 | 营收: ¥46,644</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-bold text-green-600">32.5%</p>
                                        <p class="text-xs text-muted-foreground">占比</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-muted rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-secondary rounded-lg flex items-center justify-center">
                                            <span class="text-sm font-bold text-secondary-foreground">2</span>
                                        </div>
                                        <div>
                                            <p class="font-medium">美容套餐B</p>
                                            <p class="text-sm text-muted-foreground">销量: 128 | 营收: ¥76,672</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-bold text-green-600">26.7%</p>
                                        <p class="text-xs text-muted-foreground">占比</p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-muted rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                                            <span class="text-sm font-bold text-accent-foreground">3</span>
                                        </div>
                                        <div>
                                            <p class="font-medium">娱乐套餐C</p>
                                            <p class="text-sm text-muted-foreground">销量: 95 | 营收: ¥18,905</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-bold text-green-600">19.8%</p>
                                        <p class="text-xs text-muted-foreground">占比</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 支付方式统计 -->
                        <div class="stat-card">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">支付方式统计</h3>
                                <button class="text-sm text-primary hover:underline">查看详情</button>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-blue-500 rounded"></div>
                                        <span class="font-medium">移动支付</span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <div class="w-32 bg-muted rounded-full h-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: 65%"></div>
                                        </div>
                                        <span class="text-sm font-bold">65%</span>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-green-500 rounded"></div>
                                        <span class="font-medium">刷卡</span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <div class="w-32 bg-muted rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 25%"></div>
                                        </div>
                                        <span class="text-sm font-bold">25%</span>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-orange-500 rounded"></div>
                                        <span class="font-medium">现金</span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <div class="w-32 bg-muted rounded-full h-2">
                                            <div class="bg-orange-500 h-2 rounded-full" style="width: 10%"></div>
                                        </div>
                                        <span class="text-sm font-bold">10%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();

        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = ['dashboard-content', 'packages-content', 'members-content', 'consumption-content', 'analytics-content'];
            pages.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.classList.add('hidden');
                }
            });

            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.remove('hidden');
                targetPage.classList.add('fade-in');
            }

            // 更新侧边栏激活状态
            document.querySelectorAll('.sidebar-item').forEach(item => {
                item.classList.remove('sidebar-active');
            });

            // 激活当前页面的侧边栏项
            const activeItem = document.querySelector(`[href="#${pageId.replace('-content', '')}"]`);
            if (activeItem) {
                activeItem.classList.add('sidebar-active');
            }

            // 根据页面初始化特定功能
            if (pageId === 'analytics-content') {
                setTimeout(() => {
                    initAnalyticsCharts();
                }, 100);
            }
        }

        // 侧边栏导航事件
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const href = item.getAttribute('href');
                const pageId = href.substring(1) + '-content';
                showPage(pageId);
            });
        });

        // 初始化营收趋势图表
        function initRevenueChart() {
            const ctx = document.getElementById('revenueChart');
            if (!ctx) return;

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    datasets: [{
                        label: '营收',
                        data: [8500, 9200, 8800, 9500, 10200, 11800, 9800],
                        borderColor: 'oklch(0.6231 0.1880 259.8145)',
                        backgroundColor: 'oklch(0.6231 0.1880 259.8145 / 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    elements: {
                        point: {
                            radius: 4,
                            hoverRadius: 6
                        }
                    }
                }
            });
        }

        // 初始化统计报表图表
        function initAnalyticsCharts() {
            // 营收趋势图
            const analyticsCtx = document.getElementById('analyticsRevenueChart');
            if (analyticsCtx) {
                new Chart(analyticsCtx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                        datasets: [{
                            label: '营收',
                            data: [65000, 72000, 68000, 85000, 92000, 88000, 95000, 102000, 98000, 105000, 112000, 118000],
                            borderColor: 'oklch(0.6231 0.1880 259.8145)',
                            backgroundColor: 'oklch(0.6231 0.1880 259.8145 / 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '¥' + (value / 1000) + 'K';
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // 会员vs非会员消费占比饼图
            const membershipCtx = document.getElementById('membershipChart');
            if (membershipCtx) {
                new Chart(membershipCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['会员消费', '非会员消费'],
                        datasets: [{
                            data: [68.5, 31.5],
                            backgroundColor: [
                                'oklch(0.6231 0.1880 259.8145)',
                                'oklch(0.5461 0.2152 262.8809)'
                            ],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            }
                        }
                    }
                });
            }
        }

        // 搜索功能
        function initSearch() {
            const searchInputs = document.querySelectorAll('input[type="text"]');
            searchInputs.forEach(input => {
                input.addEventListener('input', (e) => {
                    const searchTerm = e.target.value.toLowerCase();
                    console.log('搜索:', searchTerm);
                });
            });
        }

        // 表格行悬停效果
        function initTableEffects() {
            const tableRows = document.querySelectorAll('.table-row');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', () => {
                    row.style.transform = 'translateX(4px)';
                });

                row.addEventListener('mouseleave', () => {
                    row.style.transform = 'translateX(0)';
                });
            });
        }

        // 按钮点击效果
        function initButtonEffects() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('click', (e) => {
                    // 创建涟漪效果
                    const ripple = document.createElement('span');
                    const rect = button.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255, 255, 255, 0.3);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.4s ease-out;
                        pointer-events: none;
                    `;

                    button.style.position = 'relative';
                    button.style.overflow = 'hidden';
                    button.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 400);
                });
            });
        }

        // 添加涟漪动画CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initRevenueChart();
            initSearch();
            initTableEffects();
            initButtonEffects();

            // 添加页面加载动画
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.3s ease';
                document.body.style.opacity = '1';
            }, 100);
        });

        // 暗色模式切换
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
            localStorage.setItem('darkMode', document.documentElement.classList.contains('dark'));
        }

        // 加载保存的主题设置
        if (localStorage.getItem('darkMode') === 'true') {
            document.documentElement.classList.add('dark');
        }

        // 绑定暗色模式切换按钮
        document.querySelector('[data-lucide="moon"]').parentElement.addEventListener('click', toggleDarkMode);
    </script>
</body>
</html>
