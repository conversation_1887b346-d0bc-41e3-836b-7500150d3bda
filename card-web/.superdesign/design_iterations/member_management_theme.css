/* 会员管理系统主题 - 现代商务风格 */
:root {
  /* 基础颜色 */
  --background: oklch(0.9850 0 0);
  --foreground: oklch(0.1450 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.1450 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.1450 0 0);
  
  /* 主色调 - 专业蓝色 */
  --primary: oklch(0.6231 0.1880 259.8145);
  --primary-foreground: oklch(1.0000 0 0);
  
  /* 次要颜色 */
  --secondary: oklch(0.9670 0.0029 264.5419);
  --secondary-foreground: oklch(0.4461 0.0263 256.8018);
  
  /* 静音色调 */
  --muted: oklch(0.9846 0.0017 247.8389);
  --muted-foreground: oklch(0.5510 0.0234 264.3637);
  
  /* 强调色 */
  --accent: oklch(0.9514 0.0250 236.8242);
  --accent-foreground: oklch(0.3791 0.1378 265.5222);
  
  /* 危险色 */
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  
  /* 成功色 */
  --success: oklch(0.6500 0.1500 142.0000);
  --success-foreground: oklch(1.0000 0 0);
  
  /* 警告色 */
  --warning: oklch(0.7500 0.1800 85.0000);
  --warning-foreground: oklch(0.1000 0 0);
  
  /* 边框和输入 */
  --border: oklch(0.9276 0.0058 264.5313);
  --input: oklch(0.9276 0.0058 264.5313);
  --ring: oklch(0.6231 0.1880 259.8145);
  
  /* 图表颜色 */
  --chart-1: oklch(0.6231 0.1880 259.8145);
  --chart-2: oklch(0.5461 0.2152 262.8809);
  --chart-3: oklch(0.4882 0.2172 264.3763);
  --chart-4: oklch(0.4244 0.1809 265.6377);
  --chart-5: oklch(0.3791 0.1378 265.5222);
  
  /* 侧边栏 */
  --sidebar: oklch(0.9846 0.0017 247.8389);
  --sidebar-foreground: oklch(0.3211 0 0);
  --sidebar-primary: oklch(0.6231 0.1880 259.8145);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9514 0.0250 236.8242);
  --sidebar-accent-foreground: oklch(0.3791 0.1378 265.5222);
  --sidebar-border: oklch(0.9276 0.0058 264.5313);
  --sidebar-ring: oklch(0.6231 0.1880 259.8145);
  
  /* 字体 */
  --font-sans: 'Inter', 'Roboto', sans-serif;
  --font-serif: 'Source Serif 4', serif;
  --font-mono: 'JetBrains Mono', monospace;
  
  /* 圆角 */
  --radius: 0.375rem;
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  
  /* 阴影 */
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  
  /* 间距 */
  --spacing: 0.25rem;
  --tracking-normal: 0em;
}

/* 暗色模式 */
.dark {
  --background: oklch(0.2046 0 0);
  --foreground: oklch(0.9219 0 0);
  --card: oklch(0.2686 0 0);
  --card-foreground: oklch(0.9219 0 0);
  --popover: oklch(0.2686 0 0);
  --popover-foreground: oklch(0.9219 0 0);
  --primary: oklch(0.6231 0.1880 259.8145);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.2686 0 0);
  --secondary-foreground: oklch(0.9219 0 0);
  --muted: oklch(0.2686 0 0);
  --muted-foreground: oklch(0.7155 0 0);
  --accent: oklch(0.3791 0.1378 265.5222);
  --accent-foreground: oklch(0.8823 0.0571 254.1284);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3715 0 0);
  --input: oklch(0.3715 0 0);
  --ring: oklch(0.6231 0.1880 259.8145);
  --chart-1: oklch(0.7137 0.1434 254.6240);
  --chart-2: oklch(0.6231 0.1880 259.8145);
  --chart-3: oklch(0.5461 0.2152 262.8809);
  --chart-4: oklch(0.4882 0.2172 264.3763);
  --chart-5: oklch(0.4244 0.1809 265.6377);
  --sidebar: oklch(0.2046 0 0);
  --sidebar-foreground: oklch(0.9219 0 0);
  --sidebar-primary: oklch(0.6231 0.1880 259.8145);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.3791 0.1378 265.5222);
  --sidebar-accent-foreground: oklch(0.8823 0.0571 254.1284);
  --sidebar-border: oklch(0.3715 0 0);
  --sidebar-ring: oklch(0.6231 0.1880 259.8145);
}

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-sans) !important;
  background-color: var(--background) !important;
  color: var(--foreground) !important;
  line-height: 1.5 !important;
}

/* 工具类 */
.bg-background { background-color: var(--background) !important; }
.bg-card { background-color: var(--card) !important; }
.bg-primary { background-color: var(--primary) !important; }
.bg-secondary { background-color: var(--secondary) !important; }
.bg-muted { background-color: var(--muted) !important; }
.bg-accent { background-color: var(--accent) !important; }
.bg-destructive { background-color: var(--destructive) !important; }
.bg-success { background-color: var(--success) !important; }
.bg-warning { background-color: var(--warning) !important; }

.text-foreground { color: var(--foreground) !important; }
.text-primary { color: var(--primary) !important; }
.text-secondary { color: var(--secondary-foreground) !important; }
.text-muted { color: var(--muted-foreground) !important; }
.text-accent { color: var(--accent-foreground) !important; }
.text-destructive { color: var(--destructive) !important; }
.text-success { color: var(--success) !important; }
.text-warning { color: var(--warning) !important; }

.border { border: 1px solid var(--border) !important; }
.border-primary { border-color: var(--primary) !important; }
.border-destructive { border-color: var(--destructive) !important; }

.rounded { border-radius: var(--radius) !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }

.shadow { box-shadow: var(--shadow) !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
